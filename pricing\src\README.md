# Pricing Functions Documentation

This module provides pricing calculation functions for various software companies including Allplan and Bluebeam.

## Available Functions

### Main Calculation Function
- `calculate_price(product_name, country_code, term, seats, company_name)` - Universal pricing calculator

### Company-Specific Functions

#### Allplan
- `calculate_allplan_price(product_name, country_code, term, seats)` - Calculate Allplan pricing
- `get_allplan_countries()` - Get available countries
- `get_allplan_subscriptions(country_code)` - Get available subscriptions for a country
- `get_allplan_term_options()` - Get available term options
- `get_allplan_country_pricing(country_code)` - Get raw pricing data for a country

#### Bluebeam
- `calculate_bluebeam_price(product_name, country_code, term, seats)` - Calculate Bluebeam pricing
- `get_bluebeam_countries()` - Get available countries
- `get_bluebeam_subscriptions(country_code)` - Get available subscriptions for a country
- `get_bluebeam_term_options()` - Get available term options
- `get_bluebeam_country_pricing(country_code)` - Get raw pricing data for a country

## Usage Examples

### Allplan Pricing
```python
from prices import calculate_allplan_price, get_allplan_countries, get_allplan_subscriptions

# Get available countries
countries = get_allplan_countries()
print(f"Available countries: {countries}")  # ['US']

# Get available subscriptions for US
subscriptions = get_allplan_subscriptions('US')
print(f"US subscriptions: {subscriptions}")
# ['ALLPLAN Ultimate', 'ALLPLAN Professional', 'ALLPLAN Concept', 'ALLPLAN Basic', 'ALLPLAN Civil', 'ALLPLAN Precast', 'ALLPLAN Storage']

# Calculate price for 2 seats of ALLPLAN Ultimate for 1 year in US
result = calculate_allplan_price('ALLPLAN Ultimate', 'US', '1-year', 2)
print(result)
# {
#     'product_name': 'ALLPLAN Ultimate',
#     'country_code': 'US',
#     'term': '1-year',
#     'seats': 2,
#     'price_per_seat': 392,
#     'total_price': 784,
#     'currency': 'USD',
#     'price_per_month_str': '$392',
#     'discount': '34%',
#     'pay_now': '$4,704 + tax / seat',
#     'total_price_str': '$4,704 + tax / seat'
# }
```

### Bluebeam Pricing
```python
from prices import calculate_bluebeam_price, get_bluebeam_countries, get_bluebeam_subscriptions

# Get available countries
countries = get_bluebeam_countries()
print(f"Available countries: {countries}")  # ['US', 'DE']

# Get available subscriptions for US
subscriptions = get_bluebeam_subscriptions('US')
print(f"US subscriptions: {subscriptions}")  # ['Basic', 'Core', 'Complete']

# Calculate price for 3 seats of Basic for 1 year in US
result = calculate_bluebeam_price('Basic', 'US', '1-year', 3)
print(result)
# {
#     'product_name': 'Basic',
#     'country_code': 'US',
#     'term': '1-year',
#     'seats': 3,
#     'price_per_seat': 260,
#     'total_price': 780,
#     'currency': 'USD'
# }
```

### Universal Function
```python
from prices import calculate_price

# Calculate Allplan pricing
result = calculate_price('ALLPLAN Ultimate', 'US', '1-year', 2, 'allplan')

# Calculate Bluebeam pricing
result = calculate_price('Basic', 'US', '1-year', 3, 'bluebeam')
```

## Available Data

### Allplan
- **Countries**: US
- **Products**: ALLPLAN Ultimate, ALLPLAN Professional, ALLPLAN Concept, ALLPLAN Basic, ALLPLAN Civil, ALLPLAN Precast, ALLPLAN Storage
- **Terms**: monthly, 1-year, 3-year
- **Currency**: USD

### Bluebeam
- **Countries**: US, DE
- **Products**: Basic, Core, Complete
- **Terms**: 1-year
- **Currencies**: USD (US), EUR (DE)

## Error Handling

The functions include comprehensive error handling:
- Invalid country codes
- Invalid product names
- Invalid terms
- Missing pricing data

## Notes

- Graphisoft and Vectorworks functions are placeholders and will raise `NotImplementedError`
- The module automatically handles missing JSON files gracefully
- All prices are calculated per seat and multiplied by the number of seats requested
