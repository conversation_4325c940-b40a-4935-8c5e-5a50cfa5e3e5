# Pricing Functions Documentation

This module provides pricing calculation functions for various software companies including Allplan, Bluebeam, and Graphisoft.

## Available Functions

### Main Calculation Function
- `calculate_price(product_name, country_code, term, seats, company_name)` - Universal pricing calculator

### Company-Specific Functions

#### Allplan
- `calculate_allplan_price(product_name, country_code, term, seats)` - Calculate Allplan pricing
- `get_allplan_countries()` - Get available countries
- `get_allplan_subscriptions(country_code)` - Get available subscriptions for a country
- `get_allplan_term_options()` - Get available term options
- `get_allplan_country_pricing(country_code)` - Get raw pricing data for a country

#### Bluebeam
- `calculate_bluebeam_price(product_name, country_code, term, seats)` - Calculate Bluebeam pricing
- `get_bluebeam_countries()` - Get available countries
- `get_bluebeam_subscriptions(country_code)` - Get available subscriptions for a country
- `get_bluebeam_term_options()` - Get available term options
- `get_bluebeam_country_pricing(country_code)` - Get raw pricing data for a country

#### Graphisoft
- `calculate_graphisoft_price(product_name, country_code, term, seats)` - Calculate Graphisoft pricing
- `get_graphisoft_countries()` - Get available countries
- `get_graphisoft_subscriptions(country_code)` - Get available subscriptions for a country
- `get_graphisoft_term_options()` - Get available term options
- `get_graphisoft_country_pricing(country_code)` - Get raw pricing data for a country

## Usage Examples

### Graphisoft Pricing (New Structure)
```python
from prices import calculate_graphisoft_price, get_graphisoft_countries, get_graphisoft_subscriptions

# Get available countries (252 countries available)
countries = get_graphisoft_countries()
print(f"Available countries: {len(countries)} countries")  # 252 countries

# Get available subscriptions for Aruba (AW)
subscriptions = get_graphisoft_subscriptions('AW')
print(f"AW subscriptions: {subscriptions}")
# ['Archicad Solo', 'Archicad Studio', 'Archicad Collaborate', 'BIMcloud SaaS']

# Calculate price for 2 seats of Archicad Studio for 1 year in Aruba
result = calculate_graphisoft_price('Archicad Studio', 'AW', '1-year', 2)
print(result)
# {
#     'product_name': 'Archicad Studio',
#     'country_code': 'AW',
#     'country_name': 'Aruba',
#     'term': '1-year',
#     'seats': 2,
#     'price_per_month': 201.0,
#     'price_per_seat_total': 2414.0,
#     'total_price': 4828.0,
#     'currency': 'USD',
#     'commitment': {'value': 12, 'unit': 'months'},
#     'billing': {'cycle': 'annual', 'price_per_cycle': 2414.0}
# }

# Different country with EUR pricing (Albania)
result_eur = calculate_graphisoft_price('Archicad Studio', 'AL', '1-year', 1)
print(f"Albania pricing: {result_eur['total_price']} {result_eur['currency']}")
# Albania pricing: 2151.0 EUR
```

### Allplan Pricing
```python
from prices import calculate_allplan_price, get_allplan_countries, get_allplan_subscriptions

# Get available countries
countries = get_allplan_countries()
print(f"Available countries: {countries}")  # ['US']

# Calculate price for 2 seats of ALLPLAN Ultimate for 1 year in US
result = calculate_allplan_price('ALLPLAN Ultimate', 'US', '1-year', 2)
print(f"Total: {result['total_price']} {result['currency']}")  # Total: 784 USD
```

### Bluebeam Pricing
```python
from prices import calculate_bluebeam_price, get_bluebeam_countries

# Get available countries
countries = get_bluebeam_countries()
print(f"Available countries: {countries}")  # ['US', 'DE']

# Calculate price for 3 seats of Basic for 1 year in US
result = calculate_bluebeam_price('Basic', 'US', '1-year', 3)
print(f"Total: {result['total_price']} {result['currency']}")  # Total: 780 USD
```

### Universal Function
```python
from prices import calculate_price

# Calculate Graphisoft pricing
result = calculate_price('Archicad Studio', 'AW', '1-year', 2, 'graphisoft')

# Calculate Allplan pricing
result = calculate_price('ALLPLAN Ultimate', 'US', '1-year', 2, 'allplan')

# Calculate Bluebeam pricing
result = calculate_price('Basic', 'US', '1-year', 3, 'bluebeam')
```

## Available Data

### Allplan
- **Countries**: US
- **Products**: ALLPLAN Ultimate, ALLPLAN Professional, ALLPLAN Concept, ALLPLAN Basic, ALLPLAN Civil, ALLPLAN Precast, ALLPLAN Storage
- **Terms**: monthly, 1-year, 3-year
- **Currency**: USD

### Bluebeam
- **Countries**: US, DE
- **Products**: Basic, Core, Complete
- **Terms**: 1-year
- **Currencies**: USD (US), EUR (DE)

### Graphisoft
- **Countries**: 252 countries worldwide (AW, AF, AI, AL, AO, AX, AD, etc.)
- **Products**: Archicad Solo, Archicad Studio, Archicad Collaborate, BIMcloud SaaS, DDScad Electrical Collaborate (varies by country)
- **Terms**: monthly, 1-year, 3-year (varies by product and country)
- **Currencies**: USD, EUR (varies by country)

## Error Handling

The functions include comprehensive error handling:
- Invalid country codes
- Invalid product names
- Invalid terms
- Missing pricing data
- Countries with no available subscriptions

## Notes

- Vectorworks functions are placeholders and will raise `NotImplementedError`
- The module automatically handles missing JSON files gracefully
- All prices are calculated per seat and multiplied by the number of seats requested
- Graphisoft has the most comprehensive global coverage with 252 countries
- Different countries may have different available products and pricing structures
