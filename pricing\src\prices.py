import json
import os


ALLPLAN_DIR = os.path.dirname(os.path.abspath(__file__))
file_path = os.path.join(ALLPLAN_DIR, "allplan_pricing_data.json")

with open(file_path, "r", encoding="utf-8") as allplan_pricing_data:
    allplan_pricing_data = json.loads(allplan_pricing_data.read())

BLUEBEAM_DIR = os.path.dirname(os.path.abspath(__file__))
file_path = os.path.join(BLUEBEAM_DIR, "bluebeam_pricing_data.json")

with open(file_path, "r", encoding="utf-8") as bluebeam_pricing_data:
    bluebeam_pricing_data = json.loads(bluebeam_pricing_data.read())
    
GRAPHISOFT_DIR = os.path.dirname(os.path.abspath(__file__))
file_path = os.path.join(GRAPHISOFT_DIR, "graphisoft_pricing_data.json")

with open(file_path, "r", encoding="utf-8") as graphisoft_pricing_data:
    graphisoft_pricing_data = json.loads(graphisoft_pricing_data.read())

VECTORWORKS_DIR = os.path.dirname(os.path.abspath(__file__))
file_path = os.path.join(VECTORWORKS_DIR, "vectorworks_pricing_data.json")

with open(file_path, "r", encoding="utf-8") as vectorworks_pricing_data:
    vectorworks_pricing_data = json.loads(vectorworks_pricing_data.read())


def calculate_price(product_name: str, country_code: str, term: str, seats: int, company_name: str):
    if company_name.lower() == "allplan":
        return calculate_allplan_price(product_name, country_code, term, seats)
    elif company_name.lower() == "bluebeam":
        return calculate_bluebeam_price(product_name, country_code, term, seats)
    elif company_name.lower() == "graphisoft":
        return calculate_graphisoft_price(product_name, country_code, term, seats)
    elif company_name.lower() == "vectorworks":
        return calculate_vectorworks_price(product_name, country_code, term, seats)
    else:
        raise ValueError(f"Company '{company_name}' not found.")
    
def calculate_allplan_price(product_name: str, country_code: str, term: str, seats: int):
    pass

def calculate_bluebeam_price(product_name: str, country_code: str, term: str, seats: int):
    pass

def calculate_graphisoft_price(product_name: str, country_code: str, term: str, seats: int):
    pass

def calculate_vectorworks_price(product_name: str, country_code: str, term: str, seats: int):
    pass

def get_countries(company_name: str):
    if company_name.lower() == "allplan":
        return get_allplan_countries()
    elif company_name.lower() == "bluebeam":
        return get_bluebeam_countries()
    elif company_name.lower() == "graphisoft":
        return get_graphisoft_countries()
    elif company_name.lower() == "vectorworks":
        return get_vectorworks_countries()
    else:
        raise ValueError(f"Company '{company_name}' not found.")
    
def get_allplan_countries():
    return allplan_pricing_data["countries"]

def get_bluebeam_countries():
    return bluebeam_pricing_data["countries"]

def get_graphisoft_countries():
    return graphisoft_pricing_data["countries"]

def get_vectorworks_countries():
    return vectorworks_pricing_data["countries"]

def get_term_options(company_name: str):
    if company_name.lower() == "allplan":
        return get_allplan_term_options()
    elif company_name.lower() == "bluebeam":
        return get_bluebeam_term_options()
    elif company_name.lower() == "graphisoft":
        return get_graphisoft_term_options()
    elif company_name.lower() == "vectorworks":
        return get_vectorworks_term_options()
    else:
        raise ValueError(f"Company '{company_name}' not found.")
    
def get_allplan_term_options():
    return allplan_pricing_data["term_options"]

def get_bluebeam_term_options():
    return bluebeam_pricing_data["term_options"]

def get_graphisoft_term_options():
    return graphisoft_pricing_data["term_options"]

def get_vectorworks_term_options():
    return vectorworks_pricing_data["term_options"]

def get_country_pricing(company_name: str, country_code: str):
    if company_name.lower() == "allplan":
        return get_allplan_country_pricing(country_code)
    elif company_name.lower() == "bluebeam":
        return get_bluebeam_country_pricing(country_code)
    elif company_name.lower() == "graphisoft":
        return get_graphisoft_country_pricing(country_code)
    elif company_name.lower() == "vectorworks":
        return get_vectorworks_country_pricing(country_code)
    else:
        raise ValueError(f"Company '{company_name}' not found.")
    
def get_allplan_country_pricing(country_code: str):
    return allplan_pricing_data["pricing"][country_code]

def get_bluebeam_country_pricing(country_code: str):
    return bluebeam_pricing_data["pricing"][country_code]

def get_graphisoft_country_pricing(country_code: str):
    return graphisoft_pricing_data["pricing"][country_code]

def get_vectorworks_country_pricing(country_code: str):
    return vectorworks_pricing_data["pricing"][country_code]

def get_subscriptions(company_name: str, country_code: str):
    if company_name.lower() == "allplan":
        return get_allplan_subscriptions(country_code)
    elif company_name.lower() == "bluebeam":
        return get_bluebeam_subscriptions(country_code)
    elif company_name.lower() == "graphisoft":
        return get_graphisoft_subscriptions(country_code)
    elif company_name.lower() == "vectorworks":
        return get_vectorworks_subscriptions(country_code)
    else:
        raise ValueError(f"Company '{company_name}' not found.")
    
def get_allplan_subscriptions(country_code: str):
    return allplan_pricing_data["subscriptions"][country_code]

def get_bluebeam_subscriptions(country_code: str):
    return bluebeam_pricing_data["subscriptions"][country_code]

def get_graphisoft_subscriptions(country_code: str):
    return graphisoft_pricing_data["subscriptions"][country_code]

def get_vectorworks_subscriptions(country_code: str):
    return vectorworks_pricing_data["subscriptions"][country_code]

