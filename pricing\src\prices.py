import json
import os
from typing import Dict, List, Any, Optional


# Get the directory containing this script
PRICING_DIR = os.path.dirname(os.path.abspath(__file__))

# Company configuration with data structure types
COMPANIES = {
    "allplan": {
        "file": "allplan_pricing_data.json",
        "structure": "legacy",  # country -> regions -> subscriptions -> terms
        "data": {}
    },
    "bluebeam": {
        "file": "bluebeam_pricing_data.json",
        "structure": "legacy",  # country -> regions -> subscriptions -> terms
        "data": {}
    },
    "graphisoft": {
        "file": "graphisoft_pricing_data.json",
        "structure": "standard",  # country -> subscriptions -> terms[]
        "data": {}
    },
    "vectorworks": {
        "file": "vectorworks_pricing_data.json",
        "structure": "standard",  # country -> subscriptions -> terms[]
        "data": {}
    }
}

# Load all pricing data
def load_pricing_data():
    """Load pricing data for all companies."""
    for company_name, config in COMPANIES.items():
        file_path = os.path.join(PRICING_DIR, config["file"])
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                config["data"] = json.load(f)
        except FileNotFoundError:
            print(f"Warning: {file_path} not found. {company_name.title()} pricing functions will not work.")
            config["data"] = {}

# Load data on module import
load_pricing_data()

# Legacy data access for backward compatibility
allplan_pricing_data = COMPANIES["allplan"]["data"]
bluebeam_pricing_data = COMPANIES["bluebeam"]["data"]
graphisoft_pricing_data = COMPANIES["graphisoft"]["data"]
vectorworks_pricing_data = COMPANIES["vectorworks"]["data"]


# Generic pricing functions that work with both data structures
class PricingEngine:
    """Generic pricing engine that handles different JSON structures."""

    @staticmethod
    def get_countries(company_name: str) -> List[str]:
        """Get available countries for a company."""
        if company_name.lower() not in COMPANIES:
            raise ValueError(f"Company '{company_name}' not found.")

        data = COMPANIES[company_name.lower()]["data"]
        if not data:
            return []
        return list(data.keys())

    @staticmethod
    def get_subscriptions(company_name: str, country_code: str) -> List[str]:
        """Get available subscriptions for a company and country."""
        if company_name.lower() not in COMPANIES:
            raise ValueError(f"Company '{company_name}' not found.")

        config = COMPANIES[company_name.lower()]
        data = config["data"]

        if not data:
            raise ValueError(f"{company_name.title()} pricing data not available")

        if country_code not in data:
            raise ValueError(f"Country '{country_code}' not found in {company_name.title()} pricing data")

        country_data = data[country_code]

        if config["structure"] == "legacy":
            # Legacy structure: country -> regions -> subscriptions
            subscriptions = []
            for region in country_data.get("regions", []):
                for subscription in region.get("subscriptions", []):
                    subscriptions.append(subscription["subscription"])
            return subscriptions
        else:
            # Standard structure: country -> subscriptions
            return list(country_data.get("subscriptions", {}).keys())

    @staticmethod
    def get_term_options(company_name: str) -> List[str]:
        """Get available term options for a company."""
        if company_name.lower() not in COMPANIES:
            raise ValueError(f"Company '{company_name}' not found.")

        config = COMPANIES[company_name.lower()]
        data = config["data"]

        if not data:
            return []

        terms = set()

        if config["structure"] == "legacy":
            # Legacy structure: country -> regions -> subscriptions -> terms
            for country_data in data.values():
                for region in country_data.get("regions", []):
                    for subscription in region.get("subscriptions", []):
                        terms.update(subscription.get("terms", {}).keys())
        else:
            # Standard structure: country -> subscriptions -> terms[]
            for country_data in data.values():
                for subscription_data in country_data.get("subscriptions", {}).values():
                    for term_info in subscription_data.get("terms", []):
                        terms.add(term_info.get("term_id"))

        return list(terms)

    @staticmethod
    def calculate_price(company_name: str, product_name: str, country_code: str, term: str, seats: int) -> Dict[str, Any]:
        """Generic price calculation that works with both data structures."""
        if company_name.lower() not in COMPANIES:
            raise ValueError(f"Company '{company_name}' not found.")

        config = COMPANIES[company_name.lower()]
        data = config["data"]

        if not data:
            raise ValueError(f"{company_name.title()} pricing data not available")

        if country_code not in data:
            raise ValueError(f"Country '{country_code}' not found in {company_name.title()} pricing data")

        country_data = data[country_code]

        # Find subscription and term data based on structure
        if config["structure"] == "legacy":
            return PricingEngine._calculate_legacy_price(
                company_name, product_name, country_code, term, seats, country_data
            )
        else:
            return PricingEngine._calculate_standard_price(
                company_name, product_name, country_code, term, seats, country_data
            )

    @staticmethod
    def _calculate_legacy_price(company_name: str, product_name: str, country_code: str,
                               term: str, seats: int, country_data: Dict) -> Dict[str, Any]:
        """Calculate price for legacy structure (Allplan/Bluebeam)."""
        # Find the subscription in regions
        subscription_data = None
        for region in country_data.get("regions", []):
            for subscription in region.get("subscriptions", []):
                if subscription["subscription"] == product_name:
                    subscription_data = subscription
                    break
            if subscription_data:
                break

        if not subscription_data:
            raise ValueError(f"Product '{product_name}' not found for country '{country_code}'")

        if term not in subscription_data["terms"]:
            raise ValueError(f"Term '{term}' not available for product '{product_name}' in country '{country_code}'")

        term_data = subscription_data["terms"][term]

        if term_data.get("price_value") is None:
            raise ValueError(f"Price not available for '{product_name}' with term '{term}' in country '{country_code}'")

        price_per_seat = term_data["price_value"]
        total_price = price_per_seat * seats

        # Build result with common fields
        result = {
            "product_name": product_name,
            "country_code": country_code,
            "term": term,
            "seats": seats,
            "price_per_seat": price_per_seat,
            "total_price": total_price,
            "currency": term_data["currency"]
        }

        # Add company-specific fields if available
        if company_name.lower() == "allplan":
            result.update({
                "price_per_month_str": term_data.get("price_per_month_str"),
                "discount": term_data.get("discount"),
                "pay_now": term_data.get("pay_now"),
                "total_price_str": term_data.get("total_price")
            })

        return result

    @staticmethod
    def _calculate_standard_price(company_name: str, product_name: str, country_code: str,
                                 term: str, seats: int, country_data: Dict) -> Dict[str, Any]:
        """Calculate price for standard structure (Graphisoft/Vectorworks)."""
        # Check if country has any subscriptions
        if not country_data.get("subscriptions"):
            raise ValueError(f"No subscriptions available for country '{country_code}'")

        # Find the subscription
        if product_name not in country_data["subscriptions"]:
            raise ValueError(f"Product '{product_name}' not found for country '{country_code}'")

        subscription_data = country_data["subscriptions"][product_name]

        # Find the term in the terms array
        term_data = None
        for term_info in subscription_data.get("terms", []):
            if term_info.get("term_id") == term:
                term_data = term_info
                break

        if not term_data:
            raise ValueError(f"Term '{term}' not available for product '{product_name}' in country '{country_code}'")

        # Calculate pricing based on the term structure
        price_per_month = term_data.get("price_per_month", 0) or 0
        total_price_per_seat = term_data.get("total_price", price_per_month) or price_per_month

        if total_price_per_seat == 0:
            raise ValueError(f"Price not available for '{product_name}' with term '{term}' in country '{country_code}'")

        total_price = total_price_per_seat * seats

        # Build result
        result = {
            "product_name": product_name,
            "country_code": country_code,
            "country_name": country_data.get("country_name"),
            "term": term,
            "seats": seats,
            "price_per_month": price_per_month,
            "price_per_seat": total_price_per_seat,
            "total_price": total_price,
            "currency": term_data.get("currency"),
            "commitment": term_data.get("commitment"),
            "billing": term_data.get("billing")
        }

        return result


def calculate_price(product_name: str, country_code: str, term: str, seats: int, company_name: str):
    """
    Universal pricing calculator using the new generic engine.

    Args:
        product_name: Name of the subscription product
        country_code: Country code (e.g., "US", "DE", "AW")
        term: Term length (e.g., "monthly", "1-year", "3-year")
        seats: Number of seats
        company_name: Company name (e.g., "allplan", "bluebeam", "graphisoft", "vectorworks")

    Returns:
        dict: Contains price information including total cost
    """
    return PricingEngine.calculate_price(company_name, product_name, country_code, term, seats)
    
def calculate_allplan_price(product_name: str, country_code: str, term: str, seats: int):
    """
    Calculate the total price for Allplan subscription.

    Args:
        product_name: Name of the Allplan subscription (e.g., "ALLPLAN Ultimate")
        country_code: Country code (e.g., "US")
        term: Term length (e.g., "monthly", "1-year", "3-year")
        seats: Number of seats

    Returns:
        dict: Contains price information including total cost
    """
    if country_code not in allplan_pricing_data:
        raise ValueError(f"Country '{country_code}' not found in Allplan pricing data")

    country_data = allplan_pricing_data[country_code]

    # Find the subscription in the regions
    subscription_data = None
    for region in country_data.get("regions", []):
        for subscription in region.get("subscriptions", []):
            if subscription["subscription"] == product_name:
                subscription_data = subscription
                break
        if subscription_data:
            break

    if not subscription_data:
        raise ValueError(f"Product '{product_name}' not found for country '{country_code}'")

    if term not in subscription_data["terms"]:
        raise ValueError(f"Term '{term}' not available for product '{product_name}' in country '{country_code}'")

    term_data = subscription_data["terms"][term]

    if term_data["price_value"] is None:
        raise ValueError(f"Price not available for '{product_name}' with term '{term}' in country '{country_code}'")

    price_per_seat = term_data["price_value"]
    total_price = price_per_seat * seats

    return {
        "product_name": product_name,
        "country_code": country_code,
        "term": term,
        "seats": seats,
        "price_per_seat": price_per_seat,
        "total_price": total_price,
        "currency": term_data["currency"],
        "price_per_month_str": term_data.get("price_per_month_str"),
        "discount": term_data.get("discount"),
        "pay_now": term_data.get("pay_now"),
        "total_price_str": term_data.get("total_price")
    }

def calculate_bluebeam_price(product_name: str, country_code: str, term: str, seats: int):
    """
    Calculate the total price for Bluebeam subscription.

    Args:
        product_name: Name of the Bluebeam subscription (e.g., "Basic", "Core", "Complete")
        country_code: Country code (e.g., "US", "DE")
        term: Term length (e.g., "1-year")
        seats: Number of seats

    Returns:
        dict: Contains price information including total cost
    """
    if country_code not in bluebeam_pricing_data:
        raise ValueError(f"Country '{country_code}' not found in Bluebeam pricing data")

    country_data = bluebeam_pricing_data[country_code]

    # Find the subscription in the regions
    subscription_data = None
    for region in country_data.get("regions", []):
        for subscription in region.get("subscriptions", []):
            if subscription["subscription"] == product_name:
                subscription_data = subscription
                break
        if subscription_data:
            break

    if not subscription_data:
        raise ValueError(f"Product '{product_name}' not found for country '{country_code}'")

    if term not in subscription_data["terms"]:
        raise ValueError(f"Term '{term}' not available for product '{product_name}' in country '{country_code}'")

    term_data = subscription_data["terms"][term]

    if term_data["price_value"] is None:
        raise ValueError(f"Price not available for '{product_name}' with term '{term}' in country '{country_code}'")

    price_per_seat = term_data["price_value"]
    total_price = price_per_seat * seats

    return {
        "product_name": product_name,
        "country_code": country_code,
        "term": term,
        "seats": seats,
        "price_per_seat": price_per_seat,
        "total_price": total_price,
        "currency": term_data["currency"]
    }

def calculate_graphisoft_price(product_name: str, country_code: str, term: str, seats: int):
    """
    Calculate the total price for Graphisoft subscription.

    Args:
        product_name: Name of the Graphisoft subscription (e.g., "Archicad Studio", "Archicad Collaborate")
        country_code: Country code (e.g., "AW", "AL", "AT")
        term: Term length (e.g., "monthly", "1-year", "3-year")
        seats: Number of seats

    Returns:
        dict: Contains price information including total cost
    """
    if not graphisoft_pricing_data:
        raise ValueError("Graphisoft pricing data not available. Please ensure graphisoft_pricing_data.json exists.")

    if country_code not in graphisoft_pricing_data:
        raise ValueError(f"Country '{country_code}' not found in Graphisoft pricing data")

    country_data = graphisoft_pricing_data[country_code]

    # Check if country has any subscriptions
    if not country_data.get("subscriptions"):
        raise ValueError(f"No subscriptions available for country '{country_code}'")

    # Find the subscription
    if product_name not in country_data["subscriptions"]:
        raise ValueError(f"Product '{product_name}' not found for country '{country_code}'")

    subscription_data = country_data["subscriptions"][product_name]

    # Find the term in the terms array
    term_data = None
    for term_info in subscription_data.get("terms", []):
        if term_info.get("term_id") == term:
            term_data = term_info
            break

    if not term_data:
        raise ValueError(f"Term '{term}' not available for product '{product_name}' in country '{country_code}'")

    # Calculate pricing based on the term structure
    price_per_seat = term_data.get("price_per_month", 0)
    if price_per_seat is None:
        price_per_seat = 0

    total_price_per_seat = term_data.get("total_price", price_per_seat)
    if total_price_per_seat is None:
        total_price_per_seat = price_per_seat

    total_price = total_price_per_seat * seats

    return {
        "product_name": product_name,
        "country_code": country_code,
        "country_name": country_data.get("country_name"),
        "term": term,
        "seats": seats,
        "price_per_month": price_per_seat,
        "price_per_seat_total": total_price_per_seat,
        "total_price": total_price,
        "currency": term_data.get("currency"),
        "commitment": term_data.get("commitment"),
        "billing": term_data.get("billing")
    }

def calculate_vectorworks_price(product_name: str, country_code: str, term: str, seats: int):
    """
    Calculate the total price for Vectorworks subscription.
    Now uses the generic pricing engine.
    """
    return PricingEngine.calculate_price("vectorworks", product_name, country_code, term, seats)

def get_countries(company_name: str):
    """Get available countries for a company using the generic engine."""
    return PricingEngine.get_countries(company_name)
    
def get_allplan_countries():
    """Get list of available countries for Allplan pricing."""
    return list(allplan_pricing_data.keys())

def get_bluebeam_countries():
    """Get list of available countries for Bluebeam pricing."""
    return list(bluebeam_pricing_data.keys())

def get_graphisoft_countries():
    """Get list of available countries for Graphisoft pricing."""
    if not graphisoft_pricing_data:
        return []
    return list(graphisoft_pricing_data.keys())

def get_vectorworks_countries():
    """Get list of available countries for Vectorworks pricing."""
    return list(vectorworks_pricing_data.keys())

def get_term_options(company_name: str):
    """Get available term options for a company using the generic engine."""
    return PricingEngine.get_term_options(company_name)
    
def get_allplan_term_options():
    """Get available term options for Allplan subscriptions."""
    terms = set()
    for country_data in allplan_pricing_data.values():
        for region in country_data.get("regions", []):
            for subscription in region.get("subscriptions", []):
                terms.update(subscription.get("terms", {}).keys())
    return list(terms)

def get_bluebeam_term_options():
    """Get available term options for Bluebeam subscriptions."""
    terms = set()
    for country_data in bluebeam_pricing_data.values():
        for region in country_data.get("regions", []):
            for subscription in region.get("subscriptions", []):
                terms.update(subscription.get("terms", {}).keys())
    return list(terms)

def get_graphisoft_term_options():
    """Get available term options for Graphisoft subscriptions."""
    if not graphisoft_pricing_data:
        return []
    terms = set()
    for country_data in graphisoft_pricing_data.values():
        for subscription_data in country_data.get("subscriptions", {}).values():
            for term_info in subscription_data.get("terms", []):
                terms.add(term_info.get("term_id"))
    return list(terms)

def get_vectorworks_term_options():
    """Get available term options for Vectorworks subscriptions."""
    terms = set()
    for country_data in vectorworks_pricing_data.values():
        for region in country_data.get("regions", []):
            for subscription in region.get("subscriptions", []):
                terms.update(subscription.get("terms", {}).keys())
    return list(terms)

def get_country_pricing(company_name: str, country_code: str):
    if company_name.lower() == "allplan":
        return get_allplan_country_pricing(country_code)
    elif company_name.lower() == "bluebeam":
        return get_bluebeam_country_pricing(country_code)
    elif company_name.lower() == "graphisoft":
        return get_graphisoft_country_pricing(country_code)
    elif company_name.lower() == "vectorworks":
        return get_vectorworks_country_pricing(country_code)
    else:
        raise ValueError(f"Company '{company_name}' not found.")
    
def get_allplan_country_pricing(country_code: str):
    """Get pricing data for a specific country for Allplan."""
    if country_code not in allplan_pricing_data:
        raise ValueError(f"Country '{country_code}' not found in Allplan pricing data")
    return allplan_pricing_data[country_code]

def get_bluebeam_country_pricing(country_code: str):
    """Get pricing data for a specific country for Bluebeam."""
    if country_code not in bluebeam_pricing_data:
        raise ValueError(f"Country '{country_code}' not found in Bluebeam pricing data")
    return bluebeam_pricing_data[country_code]

def get_graphisoft_country_pricing(country_code: str):
    """Get pricing data for a specific country for Graphisoft."""
    if not graphisoft_pricing_data:
        raise ValueError("Graphisoft pricing data not available")
    if country_code not in graphisoft_pricing_data:
        raise ValueError(f"Country '{country_code}' not found in Graphisoft pricing data")
    return graphisoft_pricing_data[country_code]

def get_vectorworks_country_pricing(country_code: str):
    """Get pricing data for a specific country for Vectorworks."""
    if country_code not in vectorworks_pricing_data:
        raise ValueError(f"Country '{country_code}' not found in Vectorworks pricing data")
    return vectorworks_pricing_data[country_code]

def get_subscriptions(company_name: str, country_code: str):
    """Get available subscriptions for a company and country using the generic engine."""
    return PricingEngine.get_subscriptions(company_name, country_code)
    
def get_allplan_subscriptions(country_code: str):
    """Get available subscriptions for a specific country for Allplan."""
    if country_code not in allplan_pricing_data:
        raise ValueError(f"Country '{country_code}' not found in Allplan pricing data")

    subscriptions = []
    country_data = allplan_pricing_data[country_code]
    for region in country_data.get("regions", []):
        for subscription in region.get("subscriptions", []):
            subscriptions.append(subscription["subscription"])
    return subscriptions

def get_bluebeam_subscriptions(country_code: str):
    """Get available subscriptions for a specific country for Bluebeam."""
    if country_code not in bluebeam_pricing_data:
        raise ValueError(f"Country '{country_code}' not found in Bluebeam pricing data")

    subscriptions = []
    country_data = bluebeam_pricing_data[country_code]
    for region in country_data.get("regions", []):
        for subscription in region.get("subscriptions", []):
            subscriptions.append(subscription["subscription"])
    return subscriptions

def get_graphisoft_subscriptions(country_code: str):
    """Get available subscriptions for a specific country for Graphisoft."""
    if not graphisoft_pricing_data:
        raise ValueError("Graphisoft pricing data not available")

    if country_code not in graphisoft_pricing_data:
        raise ValueError(f"Country '{country_code}' not found in Graphisoft pricing data")

    country_data = graphisoft_pricing_data[country_code]
    subscriptions = list(country_data.get("subscriptions", {}).keys())
    return subscriptions

def get_vectorworks_subscriptions(country_code: str):
    """Get available subscriptions for a specific country for Vectorworks."""
    if country_code not in vectorworks_pricing_data:
        raise ValueError(f"Country '{country_code}' not found in Vectorworks pricing data")

    subscriptions = []
    country_data = vectorworks_pricing_data[country_code]
    for region in country_data.get("regions", []):
        for subscription in region.get("subscriptions", []):
            subscriptions.append(subscription["subscription"])
    return subscriptions

