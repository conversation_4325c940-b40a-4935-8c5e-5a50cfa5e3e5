import json
import os


# Get the directory containing this script
PRICING_DIR = os.path.dirname(os.path.abspath(__file__))

# Load Allplan pricing data
allplan_file_path = os.path.join(PRICING_DIR, "allplan_pricing_data.json")
try:
    with open(allplan_file_path, "r", encoding="utf-8") as f:
        allplan_pricing_data = json.load(f)
except FileNotFoundError:
    print(f"Warning: {allplan_file_path} not found. Allplan pricing functions will not work.")
    allplan_pricing_data = {}

# Load Bluebeam pricing data
bluebeam_file_path = os.path.join(PRICING_DIR, "bluebeam_pricing_data.json")
try:
    with open(bluebeam_file_path, "r", encoding="utf-8") as f:
        bluebeam_pricing_data = json.load(f)
except FileNotFoundError:
    print(f"Warning: {bluebeam_file_path} not found. Bluebeam pricing functions will not work.")
    bluebeam_pricing_data = {}

# Load Graphisoft pricing data
graphisoft_file_path = os.path.join(PRICING_DIR, "graphisoft_pricing_data.json")
try:
    with open(graphisoft_file_path, "r", encoding="utf-8") as f:
        graphisoft_pricing_data = json.load(f)
except FileNotFoundError:
    print(f"Warning: {graphisoft_file_path} not found. Graphisoft pricing functions will not work.")
    graphisoft_pricing_data = {}

# Load Vectorworks pricing data
vectorworks_file_path = os.path.join(PRICING_DIR, "vectorworks_pricing_data.json")
try:
    with open(vectorworks_file_path, "r", encoding="utf-8") as f:
        vectorworks_pricing_data = json.load(f)
except FileNotFoundError:
    print(f"Warning: {vectorworks_file_path} not found. Vectorworks pricing functions will not work.")
    vectorworks_pricing_data = {}


def calculate_price(product_name: str, country_code: str, term: str, seats: int, company_name: str):
    if company_name.lower() == "allplan":
        return calculate_allplan_price(product_name, country_code, term, seats)
    elif company_name.lower() == "bluebeam":
        return calculate_bluebeam_price(product_name, country_code, term, seats)
    elif company_name.lower() == "graphisoft":
        return calculate_graphisoft_price(product_name, country_code, term, seats)
    elif company_name.lower() == "vectorworks":
        return calculate_vectorworks_price(product_name, country_code, term, seats)
    else:
        raise ValueError(f"Company '{company_name}' not found.")
    
def calculate_allplan_price(product_name: str, country_code: str, term: str, seats: int):
    """
    Calculate the total price for Allplan subscription.

    Args:
        product_name: Name of the Allplan subscription (e.g., "ALLPLAN Ultimate")
        country_code: Country code (e.g., "US")
        term: Term length (e.g., "monthly", "1-year", "3-year")
        seats: Number of seats

    Returns:
        dict: Contains price information including total cost
    """
    if country_code not in allplan_pricing_data:
        raise ValueError(f"Country '{country_code}' not found in Allplan pricing data")

    country_data = allplan_pricing_data[country_code]

    # Find the subscription in the regions
    subscription_data = None
    for region in country_data.get("regions", []):
        for subscription in region.get("subscriptions", []):
            if subscription["subscription"] == product_name:
                subscription_data = subscription
                break
        if subscription_data:
            break

    if not subscription_data:
        raise ValueError(f"Product '{product_name}' not found for country '{country_code}'")

    if term not in subscription_data["terms"]:
        raise ValueError(f"Term '{term}' not available for product '{product_name}' in country '{country_code}'")

    term_data = subscription_data["terms"][term]

    if term_data["price_value"] is None:
        raise ValueError(f"Price not available for '{product_name}' with term '{term}' in country '{country_code}'")

    price_per_seat = term_data["price_value"]
    total_price = price_per_seat * seats

    return {
        "product_name": product_name,
        "country_code": country_code,
        "term": term,
        "seats": seats,
        "price_per_seat": price_per_seat,
        "total_price": total_price,
        "currency": term_data["currency"],
        "price_per_month_str": term_data.get("price_per_month_str"),
        "discount": term_data.get("discount"),
        "pay_now": term_data.get("pay_now"),
        "total_price_str": term_data.get("total_price")
    }

def calculate_bluebeam_price(product_name: str, country_code: str, term: str, seats: int):
    """
    Calculate the total price for Bluebeam subscription.

    Args:
        product_name: Name of the Bluebeam subscription (e.g., "Basic", "Core", "Complete")
        country_code: Country code (e.g., "US", "DE")
        term: Term length (e.g., "1-year")
        seats: Number of seats

    Returns:
        dict: Contains price information including total cost
    """
    if country_code not in bluebeam_pricing_data:
        raise ValueError(f"Country '{country_code}' not found in Bluebeam pricing data")

    country_data = bluebeam_pricing_data[country_code]

    # Find the subscription in the regions
    subscription_data = None
    for region in country_data.get("regions", []):
        for subscription in region.get("subscriptions", []):
            if subscription["subscription"] == product_name:
                subscription_data = subscription
                break
        if subscription_data:
            break

    if not subscription_data:
        raise ValueError(f"Product '{product_name}' not found for country '{country_code}'")

    if term not in subscription_data["terms"]:
        raise ValueError(f"Term '{term}' not available for product '{product_name}' in country '{country_code}'")

    term_data = subscription_data["terms"][term]

    if term_data["price_value"] is None:
        raise ValueError(f"Price not available for '{product_name}' with term '{term}' in country '{country_code}'")

    price_per_seat = term_data["price_value"]
    total_price = price_per_seat * seats

    return {
        "product_name": product_name,
        "country_code": country_code,
        "term": term,
        "seats": seats,
        "price_per_seat": price_per_seat,
        "total_price": total_price,
        "currency": term_data["currency"]
    }

def calculate_graphisoft_price(product_name: str, country_code: str, term: str, seats: int):
    """
    Calculate the total price for Graphisoft subscription.
    Note: Implementation pending - requires graphisoft_pricing_data.json structure.
    """
    raise NotImplementedError("Graphisoft pricing calculation not yet implemented. Please ensure graphisoft_pricing_data.json is available and properly structured.")

def calculate_vectorworks_price(product_name: str, country_code: str, term: str, seats: int):
    """
    Calculate the total price for Vectorworks subscription.
    Note: Implementation pending - requires vectorworks_pricing_data.json structure.
    """
    raise NotImplementedError("Vectorworks pricing calculation not yet implemented. Please ensure vectorworks_pricing_data.json is available and properly structured.")

def get_countries(company_name: str):
    if company_name.lower() == "allplan":
        return get_allplan_countries()
    elif company_name.lower() == "bluebeam":
        return get_bluebeam_countries()
    elif company_name.lower() == "graphisoft":
        return get_graphisoft_countries()
    elif company_name.lower() == "vectorworks":
        return get_vectorworks_countries()
    else:
        raise ValueError(f"Company '{company_name}' not found.")
    
def get_allplan_countries():
    """Get list of available countries for Allplan pricing."""
    return list(allplan_pricing_data.keys())

def get_bluebeam_countries():
    """Get list of available countries for Bluebeam pricing."""
    return list(bluebeam_pricing_data.keys())

def get_graphisoft_countries():
    """Get list of available countries for Graphisoft pricing."""
    return list(graphisoft_pricing_data.keys())

def get_vectorworks_countries():
    """Get list of available countries for Vectorworks pricing."""
    return list(vectorworks_pricing_data.keys())

def get_term_options(company_name: str):
    if company_name.lower() == "allplan":
        return get_allplan_term_options()
    elif company_name.lower() == "bluebeam":
        return get_bluebeam_term_options()
    elif company_name.lower() == "graphisoft":
        return get_graphisoft_term_options()
    elif company_name.lower() == "vectorworks":
        return get_vectorworks_term_options()
    else:
        raise ValueError(f"Company '{company_name}' not found.")
    
def get_allplan_term_options():
    """Get available term options for Allplan subscriptions."""
    terms = set()
    for country_data in allplan_pricing_data.values():
        for region in country_data.get("regions", []):
            for subscription in region.get("subscriptions", []):
                terms.update(subscription.get("terms", {}).keys())
    return list(terms)

def get_bluebeam_term_options():
    """Get available term options for Bluebeam subscriptions."""
    terms = set()
    for country_data in bluebeam_pricing_data.values():
        for region in country_data.get("regions", []):
            for subscription in region.get("subscriptions", []):
                terms.update(subscription.get("terms", {}).keys())
    return list(terms)

def get_graphisoft_term_options():
    """Get available term options for Graphisoft subscriptions."""
    terms = set()
    for country_data in graphisoft_pricing_data.values():
        for region in country_data.get("regions", []):
            for subscription in region.get("subscriptions", []):
                terms.update(subscription.get("terms", {}).keys())
    return list(terms)

def get_vectorworks_term_options():
    """Get available term options for Vectorworks subscriptions."""
    terms = set()
    for country_data in vectorworks_pricing_data.values():
        for region in country_data.get("regions", []):
            for subscription in region.get("subscriptions", []):
                terms.update(subscription.get("terms", {}).keys())
    return list(terms)

def get_country_pricing(company_name: str, country_code: str):
    if company_name.lower() == "allplan":
        return get_allplan_country_pricing(country_code)
    elif company_name.lower() == "bluebeam":
        return get_bluebeam_country_pricing(country_code)
    elif company_name.lower() == "graphisoft":
        return get_graphisoft_country_pricing(country_code)
    elif company_name.lower() == "vectorworks":
        return get_vectorworks_country_pricing(country_code)
    else:
        raise ValueError(f"Company '{company_name}' not found.")
    
def get_allplan_country_pricing(country_code: str):
    """Get pricing data for a specific country for Allplan."""
    if country_code not in allplan_pricing_data:
        raise ValueError(f"Country '{country_code}' not found in Allplan pricing data")
    return allplan_pricing_data[country_code]

def get_bluebeam_country_pricing(country_code: str):
    """Get pricing data for a specific country for Bluebeam."""
    if country_code not in bluebeam_pricing_data:
        raise ValueError(f"Country '{country_code}' not found in Bluebeam pricing data")
    return bluebeam_pricing_data[country_code]

def get_graphisoft_country_pricing(country_code: str):
    """Get pricing data for a specific country for Graphisoft."""
    if country_code not in graphisoft_pricing_data:
        raise ValueError(f"Country '{country_code}' not found in Graphisoft pricing data")
    return graphisoft_pricing_data[country_code]

def get_vectorworks_country_pricing(country_code: str):
    """Get pricing data for a specific country for Vectorworks."""
    if country_code not in vectorworks_pricing_data:
        raise ValueError(f"Country '{country_code}' not found in Vectorworks pricing data")
    return vectorworks_pricing_data[country_code]

def get_subscriptions(company_name: str, country_code: str):
    if company_name.lower() == "allplan":
        return get_allplan_subscriptions(country_code)
    elif company_name.lower() == "bluebeam":
        return get_bluebeam_subscriptions(country_code)
    elif company_name.lower() == "graphisoft":
        return get_graphisoft_subscriptions(country_code)
    elif company_name.lower() == "vectorworks":
        return get_vectorworks_subscriptions(country_code)
    else:
        raise ValueError(f"Company '{company_name}' not found.")
    
def get_allplan_subscriptions(country_code: str):
    """Get available subscriptions for a specific country for Allplan."""
    if country_code not in allplan_pricing_data:
        raise ValueError(f"Country '{country_code}' not found in Allplan pricing data")

    subscriptions = []
    country_data = allplan_pricing_data[country_code]
    for region in country_data.get("regions", []):
        for subscription in region.get("subscriptions", []):
            subscriptions.append(subscription["subscription"])
    return subscriptions

def get_bluebeam_subscriptions(country_code: str):
    """Get available subscriptions for a specific country for Bluebeam."""
    if country_code not in bluebeam_pricing_data:
        raise ValueError(f"Country '{country_code}' not found in Bluebeam pricing data")

    subscriptions = []
    country_data = bluebeam_pricing_data[country_code]
    for region in country_data.get("regions", []):
        for subscription in region.get("subscriptions", []):
            subscriptions.append(subscription["subscription"])
    return subscriptions

def get_graphisoft_subscriptions(country_code: str):
    """Get available subscriptions for a specific country for Graphisoft."""
    if country_code not in graphisoft_pricing_data:
        raise ValueError(f"Country '{country_code}' not found in Graphisoft pricing data")

    subscriptions = []
    country_data = graphisoft_pricing_data[country_code]
    for region in country_data.get("regions", []):
        for subscription in region.get("subscriptions", []):
            subscriptions.append(subscription["subscription"])
    return subscriptions

def get_vectorworks_subscriptions(country_code: str):
    """Get available subscriptions for a specific country for Vectorworks."""
    if country_code not in vectorworks_pricing_data:
        raise ValueError(f"Country '{country_code}' not found in Vectorworks pricing data")

    subscriptions = []
    country_data = vectorworks_pricing_data[country_code]
    for region in country_data.get("regions", []):
        for subscription in region.get("subscriptions", []):
            subscriptions.append(subscription["subscription"])
    return subscriptions

