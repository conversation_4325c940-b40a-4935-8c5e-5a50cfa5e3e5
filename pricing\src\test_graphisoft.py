#!/usr/bin/env python3
"""
Test script for the Graphisoft pricing functions.
"""

from prices import (
    calculate_graphisoft_price,
    get_graphisoft_countries,
    get_graphisoft_subscriptions,
    get_graphisoft_term_options,
    get_graphisoft_country_pricing
)

def test_graphisoft_functions():
    print("=== Testing Graphisoft Functions ===")
    
    # Test getting countries
    countries = get_graphisoft_countries()
    print(f"Available countries: {len(countries)} countries")
    print(f"First 10 countries: {countries[:10]}")
    
    # Test with a specific country (AW - Aruba)
    test_country = "AW"
    if test_country in countries:
        print(f"\n--- Testing with country: {test_country} ---")
        
        # Get country info
        country_data = get_graphisoft_country_pricing(test_country)
        print(f"Country name: {country_data.get('country_name')}")
        
        # Test getting subscriptions
        subscriptions = get_graphisoft_subscriptions(test_country)
        print(f"Available subscriptions: {subscriptions}")
        
        # Test getting term options
        terms = get_graphisoft_term_options()
        print(f"Available terms: {terms}")
        
        # Test price calculation for each subscription
        if subscriptions and terms:
            for subscription in subscriptions[:2]:  # Test first 2 subscriptions
                for term in terms[:2]:  # Test first 2 terms
                    try:
                        result = calculate_graphisoft_price(
                            product_name=subscription,
                            country_code=test_country,
                            term=term,
                            seats=2
                        )
                        print(f"\n{subscription} - {term} (2 seats):")
                        print(f"  Price per seat: {result['price_per_month']} {result['currency']}/month")
                        print(f"  Total per seat: {result['price_per_seat_total']} {result['currency']}")
                        print(f"  Total for 2 seats: {result['total_price']} {result['currency']}")
                        print(f"  Commitment: {result['commitment']}")
                        print(f"  Billing: {result['billing']}")
                    except Exception as e:
                        print(f"  Error with {subscription} - {term}: {e}")
    
    # Test with another country (AL - Albania) which uses EUR
    test_country_2 = "AL"
    if test_country_2 in countries:
        print(f"\n--- Testing with country: {test_country_2} ---")
        
        country_data = get_graphisoft_country_pricing(test_country_2)
        print(f"Country name: {country_data.get('country_name')}")
        
        subscriptions = get_graphisoft_subscriptions(test_country_2)
        print(f"Available subscriptions: {subscriptions}")
        
        # Test one subscription
        if subscriptions:
            try:
                result = calculate_graphisoft_price(
                    product_name=subscriptions[0],
                    country_code=test_country_2,
                    term="1-year",
                    seats=1
                )
                print(f"\n{subscriptions[0]} - 1-year (1 seat):")
                print(f"  Total price: {result['total_price']} {result['currency']}")
                print(f"  Monthly equivalent: {result['price_per_month']} {result['currency']}/month")
            except Exception as e:
                print(f"  Error: {e}")

def test_error_handling():
    print("\n=== Testing Error Handling ===")
    
    # Test invalid country
    try:
        calculate_graphisoft_price("Archicad Studio", "INVALID", "1-year", 1)
    except ValueError as e:
        print(f"✓ Correctly caught invalid country error: {e}")
    
    # Test invalid product
    try:
        calculate_graphisoft_price("INVALID PRODUCT", "AW", "1-year", 1)
    except ValueError as e:
        print(f"✓ Correctly caught invalid product error: {e}")
    
    # Test invalid term
    try:
        calculate_graphisoft_price("Archicad Studio", "AW", "invalid-term", 1)
    except ValueError as e:
        print(f"✓ Correctly caught invalid term error: {e}")
    
    # Test country with no subscriptions
    try:
        subscriptions = get_graphisoft_subscriptions("AF")  # Afghanistan has empty subscriptions
        print(f"Afghanistan subscriptions: {subscriptions}")
    except Exception as e:
        print(f"Afghanistan error: {e}")

if __name__ == "__main__":
    test_graphisoft_functions()
    test_error_handling()
