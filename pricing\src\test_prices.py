#!/usr/bin/env python3
"""
Test script for the pricing functions.
"""

from prices import (
    calculate_allplan_price,
    calculate_bluebeam_price,
    get_allplan_countries,
    get_bluebeam_countries,
    get_allplan_subscriptions,
    get_bluebeam_subscriptions,
    get_allplan_term_options,
    get_bluebeam_term_options
)

def test_allplan_functions():
    print("=== Testing Allplan Functions ===")
    
    # Test getting countries
    countries = get_allplan_countries()
    print(f"Available countries: {countries}")
    
    # Test getting subscriptions for US
    if "US" in countries:
        subscriptions = get_allplan_subscriptions("US")
        print(f"US subscriptions: {subscriptions}")
        
        # Test getting term options
        terms = get_allplan_term_options()
        print(f"Available terms: {terms}")
        
        # Test price calculation
        if subscriptions and terms:
            try:
                result = calculate_allplan_price(
                    product_name=subscriptions[0],
                    country_code="US",
                    term=terms[0] if terms else "1-year",
                    seats=2
                )
                print(f"Price calculation result: {result}")
            except Exception as e:
                print(f"Error calculating price: {e}")
    
    print()

def test_bluebeam_functions():
    print("=== Testing Bluebeam Functions ===")
    
    # Test getting countries
    countries = get_bluebeam_countries()
    print(f"Available countries: {countries}")
    
    # Test getting subscriptions for US
    if "US" in countries:
        subscriptions = get_bluebeam_subscriptions("US")
        print(f"US subscriptions: {subscriptions}")
        
        # Test getting term options
        terms = get_bluebeam_term_options()
        print(f"Available terms: {terms}")
        
        # Test price calculation
        if subscriptions and terms:
            try:
                result = calculate_bluebeam_price(
                    product_name=subscriptions[0],
                    country_code="US",
                    term=terms[0] if terms else "1-year",
                    seats=3
                )
                print(f"Price calculation result: {result}")
            except Exception as e:
                print(f"Error calculating price: {e}")
    
    print()

def test_error_handling():
    print("=== Testing Error Handling ===")

    # Test invalid country
    try:
        calculate_allplan_price("ALLPLAN Ultimate", "INVALID", "1-year", 1)
    except ValueError as e:
        print(f"✓ Correctly caught invalid country error: {e}")

    # Test invalid product
    try:
        calculate_allplan_price("INVALID PRODUCT", "US", "1-year", 1)
    except ValueError as e:
        print(f"✓ Correctly caught invalid product error: {e}")

    # Test invalid term
    try:
        calculate_allplan_price("ALLPLAN Ultimate", "US", "invalid-term", 1)
    except ValueError as e:
        print(f"✓ Correctly caught invalid term error: {e}")

    # Test Bluebeam with different countries
    try:
        result_us = calculate_bluebeam_price("Basic", "US", "1-year", 1)
        result_de = calculate_bluebeam_price("Basic", "DE", "1-year", 1)
        print(f"✓ Bluebeam US Basic: ${result_us['price_per_seat']} {result_us['currency']}")
        print(f"✓ Bluebeam DE Basic: {result_de['price_per_seat']} {result_de['currency']}")
    except Exception as e:
        print(f"Error testing different countries: {e}")

    print()

if __name__ == "__main__":
    test_allplan_functions()
    test_bluebeam_functions()
    test_error_handling()
